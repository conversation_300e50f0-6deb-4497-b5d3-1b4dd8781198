<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Shop\CartController;
use App\Http\Controllers\Shop\CheckoutController;

Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
Route::get('/cart/add/{product}', [CartController::class, 'add'])->name('cart.add');
Route::post('/cart/update/{product}', [CartController::class, 'update'])->name('cart.update');
Route::post('/cart/delete/{product}', [CartController::class, 'remove'])->name('cart.delete');

Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout');
Route::post('/checkout', [CheckoutController::class, 'processOrder'])->name('checkout.process');
Route::get('/payment/continue/{order:uuid}', [CheckoutController::class, 'continuePayment'])->name('payment.continue');
Route::post('/payment/notify', [CheckoutController::class, 'payuNotify'])->name('payment.notify');

Route::prefix('checkout')->name('shop.checkout.')->group(function () {
    Route::post('/update-delivery-method', [CheckoutController::class, 'updateDeliveryMethod'])
        ->name('update-delivery-method');
    Route::post('/update-delivery-address', [CheckoutController::class, 'updateDeliveryAddress'])
        ->name('update-delivery-address');
});
