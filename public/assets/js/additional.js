(function($) {
    'use strict';

    if (!Cookies.get('aggreed')) {        
        $('#cookie_layout').removeClass('d-none');
    }

    $('.validate-form input').on('keyup', function () {
        $(this).removeClass('is-invalid');
    });

    $('.validate-form input').on('change', function () {
        var name = $(this).attr('name');
        // Escape square brackets for jQuery selector
        var escapedName = name.replace(/\[/g, '\\[').replace(/\]/g, '\\]');
        $('input[name="' + escapedName + '"]').removeClass('is-invalid');
        $(this).removeClass('is-invalid');
    });

    // Set up CSRF token for AJAX requests (required for Laravel)
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });    

    // Handle click on "Do koszyka" button
    $('.andro_product-buttons').on('click', 'a.andro_btn-custom', function(e) {
        e.preventDefault();
        var $button = $(this);
        var url = $button.attr('href');
        var productId = $button.data('product-id');

        // Don't process if already in progress
        if ($button.hasClass('loading')) {
            return;
        }

        // Add loading state
        $button.addClass('loading');
        $button.html('<i class="fa fa-spinner fa-spin"></i>');
        
        preloaderShow();

        // Send AJAX request to add product to cart
        $.ajax({
            url: url,
            type: 'GET', // Change to 'POST' if your route uses POST
            success: function(response) {
                // On success, replace the button with the quantity controls
                var qtyControls = `
                    <div class="qty" data-product-id="${productId}" data-update-url="/cart/update/${productId}">
                        <span class="qty-subtract"><i class="fa fa-minus"></i></span>
                        <input type="number" name="qty" value="1">
                        <span class="qty-add"><i class="fa fa-plus"></i></span>
                    </div>
                `;
                
                // Find the parent container and update its state
                var $productContainer = $button.closest('.andro_product-buttons');
                $productContainer.parent().addClass('in-cart');
                $productContainer.html(qtyControls);

                // Update the cart icon
                updateCartIcon(response.count, response.total);
                
                // If we're on a product detail page, update any other UI elements
                if ($('.buy__block').length) {
                    $('.buy__block').addClass('in-cart');
                    if ($('.buy__block .qty').length === 0) {
                        $('.buy__block .andro_product-buttons').html(qtyControls);
                    }
                }
                
                preloaderHide();
                
                // Show a brief success message
                showCartNotification('Produkt dodany do koszyka');
            },
            error: function(xhr) {
                $button.removeClass('loading');
                $button.html('Do koszyka');
                preloaderHide();
                
                // Handle error
                console.log('Error adding to cart:', xhr);
                showCartNotification('Błąd podczas dodawania do koszyka', 'error');
            }
        });
    });
    // Handle click on delete button (Cart Page)
    $('table.cart_table').on('click', '.remove-from-cart', function(e) {
        e.preventDefault();
        if (confirm('Czy na pewno chcesz usunąć ten produkt z koszyka?')) {
            var $button = $(this);
            var deleteUrl = $button.data('delete-url');
            
            preloaderShow();
            
            $.ajax({
                url: deleteUrl,
                type: 'POST',
                data: {
                    _method: 'POST'
                },
                success: function(response) {
                    if (response.success) {
                        $button.closest('tr').remove();
                        updateCartIcon(response.count, response.total);
                        updateCartSummary(response);
                        
                        if (response.count === 0) {
                            location.reload(); // Reload if cart is empty
                        }
                    }
                    preloaderHide();
                },
                error: function() {
                    preloaderHide();
                    alert('Wystąpił błąd podczas usuwania produktu z koszyka');
                }
            });
        }
    });

    
    $('input[name="delivery_method"]').on('change', updateDeliveryMethod); 

})(jQuery);

function updateCartIcon(count, total) {
    if (count > 0) {
        // Ensure the cart content container exists
        if ($('.andro_header-cart-content').length === 0) {
            $('.andro_header-cart a').append('<div class="andro_header-cart-content"><span></span><span></span></div><div class="mobile-cart-count"></div>');
        }
        
        // Show cart content
        // $('.andro_header-cart-content').show();
        $('.andro_header-cart-content').html('<span>' + count + ' Szt.</span><span>' + total + ' zł</span>');
        $('.cart-count').text(count + ' Szt.');
        $('.cart-total').text(total + ' zł');
        $('.mobile-cart-count').text(count);
    } else {
        // Hide cart content
        $('.andro_header-cart-content').hide();
        $('.mobile-cart-count').text('');
    }
    
    // Add a brief animation to the cart icon to draw attention
    $('.andro_header-cart').addClass('pulse');
    setTimeout(function() {
        $('.andro_header-cart').removeClass('pulse');
    }, 700);
}

function scrollToElement(element)
{
    $([document.documentElement, document.body]).animate({
        scrollTop: $(element).offset().top - 30
    }, 500);
}
/**
 * 
 * @param 
 * @returns 
 */
function addOrUpdateCount(action, formData)
{
    // return false;
    console.log(formData);
    preloaderShow();
    $.get(action, formData, function( data ) {
        if (!$('.buy__block').hasClass('in-cart'))
        {
            $('.buy__block').addClass('in-cart')
        }
        if ($('#rowId').length <= 0)
        {
            $('.add-cart-form').append('<input type="hidden" name="rowId" id="rowId" value="'+data.item.rowId+'">');
        }
        updateHeaderCart(data.cart);
        $('.buy__block .qty input[name=qty]').val(data.item.qty);
        if (data.error)
        {
            $('div.cart-error').removeClass('d-none');
            $('div.cart-error .content').html(data.error);
            scrollToElement('div.cart-error');
        }
        preloaderHide();
        // location.reload();
    }).fail(function( data ) {
        
    });
}

function preloaderShow()
{
    $('.andro_preloader').removeClass('hidden');
}


function preloaderHide()
{
    $('.andro_preloader').addClass('hidden');
}

/**
 * 
 * Helpers functions
 */
function prepareNumber(number)
{
    return number.toLocaleString(
        'en-US', // leave undefined to use the visitor's browser 
                   // locale or a string like 'en-US' to override it.
        { maximumFractionDigits: 2 }
    );
}


/**
 * Checkout functions
 */

function checkCoupon()
{
    var coupon = $('input[name=coupon]').val();
    var params = {
        coupon: coupon,
    }
    $.get('/cart/coupon', params, function( data ) {
        updateCartHtml(data);
    });
}

function loginFromCheckout()
{
    var formData = {
        '_token': $('input[name=_token]').val(),
        'email': $('input[name=login-email]').val(),
        'password': $('input[name=login-password]').val(),
    };
    $('.login-form .form-control').removeClass('is-invalid');
    $('.login-form .invalid-feedback').text('');
    $('.login-form .invalid-feedback').addClass('d-none');
    $.post('/login', formData, function( data ) {
        location.reload();
    }).fail(function( data ) {
        console.log(data.responseJSON);
        // console.log(data.errors);
        if (data.responseJSON.errors)
        {
            for(var code in data.responseJSON.errors)
            {
                $('input[name=login-' + code + ']').addClass('is-invalid');
                $('#validation-' + code).text(data.responseJSON.errors[code]);
                $('#validation-' + code).removeClass('d-none');
            }
        }
        // console.log(data);
    });
}

function deleteFromCart(url)
{
    $.get(url, function( data ) {
        deletFromCart(data.id);
        updateCartHtml(data);
    });
}


function updateCount(url, count) {
    $.get(url, { qty: count }, function( data ) {
        updateCartHtml(data);
        if (data.error)
        {
            $('div.cart-error').removeClass('d-none');
            $('div.cart-error .content').html(data.error);
        }
    });
}

function deletFromCart(id)
{
    $('#' + id).remove();
}

function updateCartHtml(data)
{
    for (var pr in data.cart.items)
    {
        $('#' + pr + ' td.sum strong').text(data.cart.items[pr].subtotal + ' zł');
        var fullPrice = prepareNumber(data.cart.items[pr].price * data.cart.items[pr].qty);
        $('#' + pr + ' td.sum span').text(fullPrice + ' zł');
        if (fullPrice != data.cart.items[pr].subtotal)
        {
            $('#' + pr + ' td.sum span').removeClass('d-none');
        }
        else
        {
            $('#' + pr + ' td.sum span').addClass('d-none');
        }
        $('#' + pr + ' td.quantity input').val(data.cart.items[pr].qty);
        
    }
    if (data.cart.discount*1 > 0)
    {
        full_discount = prepareNumber(data.cart.discount);
        $('tr.discount').removeClass('d-none');
        $('tr.discount td').text('- ' + full_discount + ' zł');
    }
    else
    {
        $('tr.discount').addClass('d-none');
    }
    $('tr.razem td').text(data.cart.priceTotal + ' zł');
    updateHeaderCart(data.cart);
    
    updateTotalsAndDeliveriesInputs(data.cart);
    updateSummary();

    // Update delivery costs if available
    if (data.delivery_cost !== undefined) {
        $('#delivery-td td strong').text(data.delivery_cost + ' zł');
    }

    // Update total with delivery cost
    var total = parseFloat(data.cart.total);
    var deliveryCost = parseFloat($('#delivery-td td strong').text()) || 0;
    var finalTotal = (total + deliveryCost).toFixed(2);
    $('#total-td td strong').text(finalTotal + ' zł');
}

function updateHeaderCart(cart)
{
    if ($('.andro_header-cart-content').length <= 0)
    {
        $('.andro_header-cart a').append('<div class="andro_header-cart-content"><span></span><span></span></div><div class="mobile-cart-count"></div>');
    }
    $('.andro_header-cart-content').html('<span>' + cart.count + ' Szt.</span><span>' + cart.total + ' zł</span>');
    $('.andro_header-cart .mobile-cart-count').text(cart.count);
}

function updateTotalsAndDeliveriesInputs(cart)
{
    var total = cart.total;
    updateDeliveryCosts(cart.deliveryPrices);
    $('input[name=total_sum]').val(total);
}

function updateDeliveryCosts(deliveryCosts)
{
    for (pr in deliveryCosts)
    {
        $('#delivery-check-'+pr).data('cost', deliveryCosts[pr]);
        $("label[for='delivery-check-"+pr+"'] span").text('(+' + deliveryCosts[pr] + ' zł)');
    }
}

function getDeliveryCost()
{
    var delivery_type = $('input[name=delivery_type_selected]').val();
    var delivery_cost = 0;
    if (delivery_type)
    {
        delivery_cost = $('#delivery-check-'+delivery_type).data('cost');
    }
    return delivery_cost*1;
}

function updateSummary()
{
    var total = $('input[name=total_sum]').val();
    var delivery_cost = getDeliveryCost();
    var totalCost = prepareNumber(total*1 + delivery_cost*1);
    $('#delivery-td td strong').text(delivery_cost + ' zł');
    $('#total-td td strong').text(totalCost + ' zł');
}

function getPachkomatPoints(code)
{
    var params = {
        relative_post_code: code,
        limit: 5,
        type: 'parcel_locker,parcel_locker_superpop',
    }
    $.get('https://api-shipx-pl.easypack24.net/v1/points', params, function( data ) {
        $('#pachkomaty .list').html('');
        for (var pr in data.items)
        {
            $('#pachkomaty .list').append(getPachkomatPointHtml(data.items[pr]));
        }
        console.log(data);
    });
}

function changeDeliveryAddress(v, el)
{
    var deliveryPrice = $(el).find(':selected').data('delivery-price');
    $('#delivery-td td strong').text(deliveryPrice + ' zł');
    var totalPrice = deliveryPrice + $(el).find(':selected').data('delivery-total');
    $('#total-td td strong').text(totalPrice + ' zł');
    if (v > 0)
    {
        $('.delivery-form').addClass('d-none');
    }
    else
    {
        $('.delivery-form').removeClass('d-none');
    }
    updateSelectedDelivery();
    updateSummary();
}

function updateSelectedDelivery()
{
    var userDeliveries = $('select[name=delivery_address_id]');
    if (userDeliveries.length && userDeliveries.find(':selected').data('delivery-type') > 0)
    {        
        $('input[name=delivery_type_selected]').val(userDeliveries.find(':selected').data('delivery-type'));
        return true;
    }
    $('input[name=delivery_type_selected]').val($('input[name=delivery]:checked').val());
}

function getAdditionalDelivery()
{
    updateSelectedDelivery();
    var paczkomat = $('input[name=delivery]:checked').data('paczkomat');
    if (paczkomat)
    {
        $('#pachkomaty').removeClass('d-none');
    }
    else
    {
        $('#pachkomaty').addClass('d-none');
    }
    updateSummary();
}

function setPachkomatPoint(v)
{
    $('#pachkomat_from_map').html('');
    $('input[name=pachkomat_name]').val(v);
}

function setPachkomatPointFromMap(item)
{
    $("#easypack-mapModal").modal('hide');
    var data = '<label class="custom-control-label-two" for="in_pc_' + item.name + '">'
    data += getPachkomatAddress(item);
    data += getPachkomatName(item);
    data += '</label></div>';
    $('#pachkomat_from_map').html(data);
    $('input[name=pachkomat_name]').val(item.name);
}

function getPachkomatPointHtml(item)
{
    var returnStr = '<div class="custom-control custom-radio">';
    returnStr += '<input type="radio" class="custom-control-input" value="' + item.name + '" name="pachkomat_auto" id="in_pc_' + item.name + '" onchange="setPachkomatPoint(this.value, true)">';
    returnStr += '<label class="custom-control-label" for="in_pc_' + item.name + '">'
    returnStr += getPachkomatAddress(item);
    returnStr += getPachkomatName(item);
    returnStr += '</label></div>';
    return returnStr;
}

function getPachkomatName(item)
{
    return '<br/><span class="font-weight-normal">(Paczkomat ' + item.name + ', Godziny otwarcia: ' + item.opening_hours + ')</span>';
}

function getPachkomatAddress(item)
{
    return item.address.line1 + ', ' + item.address.line2;
}

function aggreeCookie()
{
    Cookies.set('aggreed', true, { expires: 3660, path: '' });
    $('#cookie_layout').addClass('d-none');
}

function toogleFilterMoreAndLess(id, el)
{
    var text = $(el).text() == 'więcej' ? 'mniej' : 'więcej';
    $(el).text(text);
    $('#' + id + ' .more-fields').toggleClass('d-none');
    return false;
}

// Global variable for map instance
let easyPackMap = null;

function initializeEasyPackMap() {
    if (!window.easyPack) {
        console.log('EasyPack SDK not loaded');
        return;
    }

    const mapElement = document.getElementById('easypack-map');
    if (!mapElement || mapElement.style.display === 'none') {
        return;
    }

    try {
        if (!easyPackMap) {
            easyPackMap = easyPack.mapWidget('easypack-map', function(point) {
                setPachkomatPointFromMap(point);
            });
        }
    } catch (error) {
        console.error('Error initializing map:', error);
    }
}

function showPaczkomatMap() {
    const mapElement = document.getElementById('easypack-map');
    if (!mapElement) return;

    mapElement.style.display = 'block';
    
    if (!easyPackMap) {
        initializeEasyPackMap();
    }
}

function searchPaczkomatPoints() {
    const searchInput = document.getElementById('paczkomat_search');
    if (!searchInput || !searchInput.value) return;

    showPaczkomatMap();
    
    if (easyPackMap) {
        easyPackMap.searchPlace(searchInput.value);
    } else {
        initializeEasyPackMap();
        // Retry search after initialization
        setTimeout(() => {
            if (easyPackMap) {
                easyPackMap.searchPlace(searchInput.value);
            }
        }, 500);
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    const searchButton = document.getElementById('search_paczkomat_btn');
    if (searchButton) {
        searchButton.addEventListener('click', searchPaczkomatPoints);
    }

    const searchInput = document.getElementById('paczkomat_search');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchPaczkomatPoints();
            }
        });
    }
});

function setPachkomatPointFromMap(point) {
    if (!point) return;

    const mapElement = document.getElementById('easypack-map');
    if (mapElement) {
        mapElement.style.display = 'none';
    }

    document.getElementById('paczkomat_name').value = point.name;
    
    const pointDetails = document.getElementById('paczkomat_from_map');
    if (pointDetails) {
        pointDetails.innerHTML = `
            <div class="selected-point">
                <strong>${point.name}</strong><br>
                ${point.address.line1}, ${point.address.line2}
            </div>
        `;
    }
}

// Clean up function
function destroyEasyPackMap() {
    if (easyPackMap) {
        easyPackMap = null;
    }
}

// Add cleanup on delivery method change
document.addEventListener('DOMContentLoaded', function() {
    const deliveryInputs = document.querySelectorAll('input[name="delivery_method"]');
    deliveryInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.value !== 'inpost_paczkomat') {
                destroyEasyPackMap();
            }
        });
    });
});

// Update cart summary function
function updateCartSummary(response) {
    // Convert string values to numbers
    const total = parseFloat(response.total);
    const subTotal = parseFloat(response.subTotal);
    const minCost = parseFloat(response.deliveryInfo.min_cost);
    const amountLeftForFree = parseFloat(response.deliveryInfo.amount_left_for_free);
    const freeShippingThreshold = parseFloat(response.deliveryInfo.free_shipping_threshold);

    // Update subtotal
    $('.razem td').text(subTotal.toFixed(2) + ' zł');
    
    // Update discount row
    if (subTotal === total) {
        $('.discount').addClass('d-none');
    } else {
        $('.discount').removeClass('d-none');
        $('.discount td').text('- ' + (subTotal - total).toFixed(2) + ' zł');
    }
    
    // Update delivery info
    const deliveryTd = $('#delivery-td td');
    if (response.deliveryInfo.is_free) {
        deliveryTd.html('<strong class="text-success">DARMOWA DOSTAWA</strong>');
    } else {
        let deliveryHtml = `<strong>od ${minCost.toFixed(2)} zł</strong>`;
        if (freeShippingThreshold > 0 && amountLeftForFree > 0) {
            deliveryHtml += `<br><small class="text-muted">Dodaj produkty za ${amountLeftForFree.toFixed(2)} zł, aby otrzymać darmową dostawę</small>`;
        }
        deliveryTd.html(deliveryHtml);
    }
    
    // Update total
    $('#total-td td strong').text(total.toFixed(2) + ' zł');
}

// Function to show a brief notification
function showCartNotification(message, type = 'success') {
    // Create notification element if it doesn't exist
    if ($('#cart-notification').length === 0) {
        $('body').append('<div id="cart-notification" class=""></div>');
    }
    
    var $notification = $('#cart-notification');
    $notification.attr('class', type === 'success' ? 'success' : 'error');
    $notification.text(message);
    $notification.addClass('show');
    
    // Hide after 3 seconds
    setTimeout(function() {
        $notification.removeClass('show');
    }, 3000);
}

// Handle manual change of quantity input (for newly added items too)
$('body').on('change', '.qty input[name="qty"]', function(e) {
    var $input = $(this);
    var newQty = parseInt($input.val());
    if (isNaN(newQty) || newQty < 1) {
        $input.val(1);
        newQty = 1;
    }

    var $qtyDiv = $input.closest('.qty');
    var updateUrl = $qtyDiv.data('update-url');
    var productId = $qtyDiv.data('product-id');

    // Store old value before update
    var oldValue = $input.data('old-value') || 1;
    $input.data('old-value', newQty);

    preloaderShow();

    // Send AJAX request to update quantity in cart
    $.ajax({
        url: updateUrl,
        type: 'POST',
        data: {
            quantity: newQty,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                updateCartIcon(response.count, response.total);
                
                // If we're on the cart page, update the cart summary
                if ($('table.cart_table').length) {
                    updateCartSummary(response);
                }
                
                // Update delivery costs if needed
                if (response.delivery_cost !== undefined) {
                    $('#delivery-td strong').text(response.delivery_cost + ' zł');
                }
            } else {
                // Show error in Polish
                showCartNotification('Nie wystarczająca ilość produktu w magazynie. Dostępna ilość: ' + 
                      (response.availableQuantity || 0) + ' szt.', 'error');
                      
                // Revert to previous quantity
                $input.val(oldValue);
                $input.data('old-value', oldValue);
            }
            preloaderHide();
        },
        error: function(xhr) {
            console.log('Error updating quantity:', xhr);
            preloaderHide();
            showCartNotification('Wystąpił błąd podczas aktualizacji koszyka', 'error');
            
            // Revert to previous quantity
            $input.val(oldValue);
            $input.data('old-value', oldValue);
        }
    });
});

function updateDeliveryMethod() {
    const deliveryMethodId = $('input[name="delivery_method_id"]:checked').val();
    if (!deliveryMethodId) return;

    $.ajax({
        url: '/checkout/update-delivery-method',
        type: 'POST',
        data: {
            delivery_method_id: deliveryMethodId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Update delivery cost section
                if (response.totals && response.totals.deliveryHtml) {
                    $('#delivery-cost-section').html(response.totals.deliveryHtml);
                }
                
                // Update total price
                if (response.totals && response.totals.totalFormatted) {
                    $('#total-price').text(response.totals.totalFormatted + ' zł');
                }
            }
        },
        error: function() {
            console.error('Error updating delivery method');
        }
    });
}

// Attach event listener to delivery method radio buttons

// Initialize cart functionality
function initializeCartFunctionality() {
    // Make sure CSRF token is set up for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    
    // First, unbind any existing handlers to prevent duplicates
    $('body').off('click.qtyControl', '.qty-add, .qty-subtract');
    
    // Then add a single, namespaced handler
    $('body').on('click.qtyControl', '.qty-add, .qty-subtract', function(e) {
        e.preventDefault(); // Prevent any default action
        e.stopPropagation(); // Stop event bubbling
        
        var $button = $(this);
        var $input = $button.siblings('input[name="qty"]');
        var currentVal = parseInt($input.val());
        
        if (isNaN(currentVal)) {
            currentVal = 1;
        }
        
        if ($button.hasClass('qty-add')) {
            $input.val(currentVal + 1);
        } else if ($button.hasClass('qty-subtract') && currentVal > 1) {
            $input.val(currentVal - 1);
        }
        
        // Trigger change event to update cart
        $input.trigger('change');
        
        return false; // Extra prevention of event bubbling
    });
}

// Call initialization when document is ready
$(document).ready(function() {
    initializeCartFunctionality();
});
