/* Cart notification */
#cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

#cart-notification.show {
    transform: translateY(0);
    opacity: 1;
}

#cart-notification.success {
    background-color: #4CAF50;
    color: white;
}

#cart-notification.error {
    background-color: #F44336;
    color: white;
}

/* Cart icon animation */
.andro_header-cart.pulse {
    animation: cart-pulse 0.7s ease;
}

@keyframes cart-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Mobile Cart Optimizations */
@media (max-width: 768px) {
    /* Compact mobile cart layout */
    .andro_responsive-table.cart_table {
        border: none;
        margin-bottom: 15px;
    }

    .andro_responsive-table.cart_table tr {
        background: #fff;
        border-radius: 8px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #f0f2f3;
        padding: 12px;
        display: block;
    }

    .andro_responsive-table.cart_table td {
        border: none;
        padding: 4px 0;
        display: block;
        text-align: left;
        background: transparent;
    }

    .andro_responsive-table.cart_table td::before {
        display: none;
    }

    /* Mobile cart item layout - horizontal card style */
    .andro_responsive-table.cart_table tr {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    /* Product section - horizontal layout */
    .andro_responsive-table.cart_table td[data-title="Product"] {
        order: 1;
        flex: 1;
    }

    .andro_responsive-table.cart_table .andro_cart-product-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-direction: row;
        padding: 0;
    }

    .andro_responsive-table.cart_table .andro_cart-product-wrapper img,
    .andro_responsive-table.cart_table .andro_cart-product-wrapper .cart-product-img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 6px;
        margin: 0;
        flex-shrink: 0;
        display: block !important; /* Override d-none d-md-block */
    }

    .andro_responsive-table.cart_table .andro_cart-product-body {
        flex: 1;
        min-width: 0; /* Allow text truncation */
    }

    .andro_responsive-table.cart_table .andro_cart-product-body h6 {
        font-size: 14px;
        line-height: 1.3;
        margin-bottom: 2px;
        font-weight: 600;
    }

    /* Price, quantity, and total in a horizontal row */
    .mobile-cart-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        order: 2;
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #f0f2f3;
    }

    .andro_responsive-table.cart_table td.price,
    .andro_responsive-table.cart_table td.quantity,
    .andro_responsive-table.cart_table td.sum {
        display: none; /* Hide individual cells, use mobile-cart-controls instead */
    }

    .andro_responsive-table.cart_table td.remove {
        position: absolute;
        top: 8px;
        right: 8px;
        order: 0;
        padding: 0;
    }

    .andro_responsive-table.cart_table .close-btn {
        width: 24px;
        height: 24px;
        opacity: 0.6;
    }

    .andro_responsive-table.cart_table .close-btn:hover {
        opacity: 1;
    }

    /* Mobile quantity controls */
    .mobile-qty-control {
        display: flex;
        align-items: center;
        gap: 8px;
        background: #f8f9fa;
        border-radius: 20px;
        padding: 4px 8px;
    }

    .mobile-qty-control .qty-subtract,
    .mobile-qty-control .qty-add {
        width: 32px;
        height: 32px;
        min-width: 44px; /* WCAG minimum touch target */
        min-height: 44px;
        border-radius: 50%;
        background: #fff;
        border: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 0;
    }

    .mobile-qty-control .qty-subtract:hover,
    .mobile-qty-control .qty-add:hover {
        background: #fab725;
        border-color: #fab725;
        color: #fff;
    }

    .mobile-qty-control input {
        width: 40px;
        text-align: center;
        border: none;
        background: transparent;
        font-weight: 600;
        font-size: 14px;
    }

    /* Mobile price display */
    .mobile-price {
        font-size: 14px;
        font-weight: 600;
        color: #18181d;
    }

    .mobile-price .original-price {
        font-size: 12px;
        color: #fab725;
        text-decoration: line-through;
        font-weight: 400;
        margin-right: 4px;
    }

    .mobile-total {
        font-size: 16px;
        font-weight: 700;
        color: #18181d;
    }

    /* Additional mobile optimizations */
    .andro_responsive-table.cart_table tbody {
        display: block;
    }

    /* Ensure proper spacing between cart items */
    .andro_responsive-table.cart_table tr:last-child {
        margin-bottom: 0;
    }

    /* Optimize the cart section padding */
    .section.pb-2 {
        padding-bottom: 15px !important;
    }

    /* Make the mobile cart more compact */
    .andro_responsive-table.cart_table tr {
        position: relative;
        padding: 15px;
    }

    /* Improve mobile quantity input styling */
    .mobile-qty-control input:focus {
        outline: none;
        background: rgba(250, 183, 37, 0.1);
    }

    /* Better mobile remove button positioning */
    .andro_responsive-table.cart_table td.remove {
        position: absolute;
        top: 12px;
        right: 12px;
        z-index: 2;
    }

    /* Ensure mobile controls are properly aligned */
    .mobile-cart-controls {
        align-items: center;
        flex-wrap: nowrap;
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .andro_responsive-table.cart_table .andro_cart-product-wrapper img,
        .andro_responsive-table.cart_table .andro_cart-product-wrapper .cart-product-img {
            width: 50px;
            height: 50px;
        }

        .mobile-cart-controls {
            gap: 8px;
        }

        .mobile-qty-control {
            padding: 3px 6px;
        }

        .mobile-qty-control .qty-subtract,
        .mobile-qty-control .qty-add {
            width: 28px;
            height: 28px;
            min-width: 44px; /* Maintain WCAG compliance */
            min-height: 44px;
        }

        .mobile-qty-control input {
            width: 35px;
            font-size: 13px;
        }
    }

    /* Mobile cart summary optimizations */
    .andro_cart-form .col-lg-6 {
        margin-top: 20px;
    }

    .andro_cart-form table {
        margin-bottom: 20px;
    }

    .andro_cart-form table th,
    .andro_cart-form table td {
        padding: 12px 15px;
        font-size: 14px;
    }

    .andro_cart-form .andro_btn-custom {
        padding: 12px 20px;
        font-size: 16px;
        font-weight: 600;
    }

    /* Accessibility improvements */

    .andro_responsive-table.cart_table .close-btn {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Focus states for better accessibility */
    .mobile-qty-control .qty-subtract:focus,
    .mobile-qty-control .qty-add:focus,
    .andro_responsive-table.cart_table .close-btn:focus {
        outline: 2px solid #fab725;
        outline-offset: 2px;
    }

    .mobile-qty-control input:focus {
        outline: 2px solid #fab725;
        outline-offset: 1px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .andro_responsive-table.cart_table tr {
            border: 2px solid #000;
        }

        .mobile-qty-control .qty-subtract,
        .mobile-qty-control .qty-add {
            border: 2px solid #000;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .mobile-qty-control .qty-subtract,
        .mobile-qty-control .qty-add {
            transition: none;
        }
    }
}

/* Loading state for buttons */
.andro_btn-custom.loading {
    opacity: 0.7;
    cursor: not-allowed;
}