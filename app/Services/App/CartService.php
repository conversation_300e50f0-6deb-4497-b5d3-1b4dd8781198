<?php

namespace App\Services\App;

use App\Models\Cart;
use App\Models\Product;
use App\Services\DeliveryPricingService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;

class CartService
{
    protected $cart;
    protected DeliveryPricingService $deliveryPricingService;

    /**
     * CartService constructor.
     * Initializes the cart when the service is instantiated.
     */
    public function __construct(DeliveryPricingService $deliveryPricingService)
    {
        $this->deliveryPricingService = $deliveryPricingService;
        $this->initializeCart();
    }

    /**
     * Initialize the cart.
     */
    protected function initializeCart()
    {
        if (Auth::check()) {
            // Logged-in user
            $this->cart = Cart::firstOrCreate(
                ['user_id' => Auth::id(), 'status' => 'active'],
                ['id' => (string) Str::uuid()]
            );
        } else {
            // Guest user
            $cartId = Cookie::get('cart_id');

            if ($cartId) {
                $this->cart = Cart::where('id', $cartId)
                    ->where('status', 'active')
                    ->first();

                if (!$this->cart) {
                    $this->cart = $this->createCart();
                }
            } else {
                $this->cart = $this->createCart();
            }
        }
    }

    /**
     * Get the current cart.
     *
     * @return Cart|null
     */
    public function getCart()
    {
        return $this->cart;
    }

    /**
     * Create a new cart for a guest user.
     *
     * @return Cart
     */
    protected function createCart()
    {
        $cart = Cart::create(['status' => 'active']);
        Cookie::queue('cart_id', $cart->id, 60 * 24 * 30); // 30 days
        return $cart;
    }

    public function getCartItems()
    {
        return $this->cart->items()->with('product')->get();
    }

    /**
     * Get the total number of items in the cart.
     *
     * @return int
     */
    public function getCount()
    {
        return $this->cart ? $this->cart->items->sum('quantity') : 0;
    }

    /**
     * Get the total cost of the items in the cart without discounts.
     *
     * @return float
     */
    public function getSubtotal()
    {
        $subtotal = $this->cart ? $this->cart->items->sum(function ($item) {
            return $item->quantity * $item->product->price;
        }) : 0;

        return number_format($subtotal, 2, '.', '');
    }

    /**
     * Get the total cost of the items in the cart with discounts applied.
     *
     * @return float
     */
    public function getTotal()
    {
        $total = $this->cart ? $this->cart->items->sum(function ($item) {
            $product = $item->product;
            $price = $product->discounted_price != null && $product->discounted_price < $product->price
                ? $product->discounted_price
                : $product->price;
            return $item->quantity * $price;
        }) : 0;

        return number_format($total, 2, '.', '');
    }

    /**
     * Get the delivery cost based on the selected delivery method.
     *
     * @return float
     */
    public function getDeliveryCost()
    {
        // Get the selected delivery method from the session
        $deliveryMethodId = session('delivery_method');

        if (!$deliveryMethodId) {
            return 0;
        }

        // Get delivery cost info using the new system
        $deliveryInfo = $this->getDeliveryCostInfo($deliveryMethodId);

        return $deliveryInfo['cost'] ?? 0;
    }

    /**
     * Get the total cost including delivery.
     *
     * @return float
     */
    public function getTotalWithDelivery()
    {
        $total = $this->getTotal();
        $deliveryCost = $this->getDeliveryCost();

        return number_format($total + $deliveryCost, 2, '.', '');
    }

    /**
     * Add a product to the cart.
     *
     * @param Product $product
     * @param int $quantity
     * @return array
     */
    public function addProduct(Product $product, $quantity = 1)
    {
        // Get current cart item if it exists
        $cartItem = $this->cart->items()->where('product_id', $product->id)->first();
        $currentQuantityInCart = $cartItem ? $cartItem->quantity : 0;
        $totalRequestedQuantity = $currentQuantityInCart + $quantity;

        // Check if the total requested quantity exceeds stock
        if ($totalRequestedQuantity > $product->stock) {
            $availableQuantity = $product->stock - $currentQuantityInCart;
            $availableQuantity = max($availableQuantity, 0); // Ensure non-negative

            return [
                'success' => false,
                'message' => "Only {$availableQuantity} unit(s) available in stock.",
                'availableQuantity' => $availableQuantity,
            ];
        }

        if ($cartItem) {
            $cartItem->quantity += $quantity;
            $cartItem->save();
        } else {
            $this->cart->items()->create([
                'product_id' => $product->id,
                'quantity' => $quantity,
            ]);
        }

        return [
            'success' => true,
            'message' => 'Product added to cart',
        ];
    }

    /**
     * Update the quantity of a product in the cart.
     *
     * @param Product $product
     * @param int $quantity
     * @return array
     */
    public function updateProductQuantity(Product $product, $quantity)
    {
        // Check if the requested quantity exceeds stock
        if ($quantity > $product->stock) {
            return [
                'success' => false,
                'message' => "Only {$product->stock} unit(s) available in stock.",
                'availableQuantity' => $product->stock,
            ];
        }

        $cartItem = $this->cart->items()->where('product_id', $product->id)->first();

        if ($cartItem) {
            if ($quantity > 0) {
                $cartItem->quantity = $quantity;
                $cartItem->save();
            } else {
                // Remove the item if quantity is zero or less
                $cartItem->delete();
            }
        }

        return [
            'success' => true,
            'message' => 'Cart updated',
        ];
    }

    /**
     * Remove a product from the cart.
     *
     * @param Product $product
     * @return bool
     */
    public function removeProduct(Product $product)
    {
        $cartItem = $this->cart->items()->where('product_id', $product->id)->first();

        if ($cartItem) {
            $cartItem->delete();
        }

        return true;
    }

    public function isProductInCart(Product $product)
    {
        return $this->cart->items()->where('product_id', $product->id)->exists();
    }

    /**
     * Get cart item by product.
     *
     * @param Product $product
     * @return \App\Models\CartItem|null
     */
    public function getCartItemByProduct(Product $product)
    {
        return $this->cart->items()->where('product_id', $product->id)->first();
    }

    /**
     * Get detailed delivery cost information with parcel-based calculation
     *
     * @param int|null $selectedMethodId
     * @return array
     */
    public function getDeliveryCostInfo($selectedMethodId = null)
    {
        $deliveryMethods = \App\Models\DeliveryMethod::where('is_active', true)
            ->orderBy('price', 'asc')
            ->get();

        $subtotal = $this->getSubtotal();
        $cartItems = $this->getCartItems();

        // Try parcel-based calculation for InPost methods
        if ($selectedMethodId) {
            $selectedMethod = $deliveryMethods->find($selectedMethodId);
            if ($selectedMethod && $selectedMethod->api_provider === 'inpost') {
                return $this->getParcelBasedDeliveryInfo($cartItems, $subtotal, $selectedMethod);
            } elseif ($selectedMethod) {
                // Traditional calculation for non-InPost methods
                return $this->getTraditionalDeliveryInfo($subtotal, $selectedMethod);
            }
        }

        // Default calculation for all methods
        if ($deliveryMethods->isEmpty()) {
            return [
                'is_free' => false,
                'min_cost' => 0,
                'costs' => [],
                'free_shipping_threshold' => 0,
                'amount_left_for_free' => 0,
                'calculation_method' => 'none'
            ];
        }

        // Check if we have InPost methods for parcel calculation
        $inpostMethods = $deliveryMethods->where('api_provider', 'inpost');
        if ($inpostMethods->isNotEmpty()) {
            $defaultInpostMethod = $inpostMethods->first();
            $parcelInfo = $this->getParcelBasedDeliveryInfo($cartItems, $subtotal, $defaultInpostMethod);

            // Add traditional fallback info
            $traditionalInfo = $this->getTraditionalDeliveryInfoForAllMethods($deliveryMethods, $subtotal);

            return array_merge($parcelInfo, [
                'traditional_fallback' => $traditionalInfo,
                'available_methods' => $deliveryMethods->map(function($method) use ($subtotal) {
                    $isFree = $method->min_cart_amount > 0 && $subtotal >= $method->min_cart_amount;
                    return [
                        'id' => $method->id,
                        'name' => $method->name,
                        'price' => $method->price,
                        'is_free' => $isFree,
                        'cost' => $isFree ? 0 : $method->price,
                        'api_provider' => $method->api_provider
                    ];
                })
            ]);
        }

        // Fallback to traditional calculation
        return $this->getTraditionalDeliveryInfoForAllMethods($deliveryMethods, $subtotal);
    }

    /**
     * Get parcel-based delivery information
     *
     * @param \Illuminate\Support\Collection $cartItems
     * @param float $subtotal
     * @param \App\Models\DeliveryMethod $deliveryMethod
     * @return array
     */
    protected function getParcelBasedDeliveryInfo($cartItems, float $subtotal, $deliveryMethod): array
    {
        try {
            $deliveryInfo = $this->deliveryPricingService->getDeliveryInfo($cartItems, $subtotal);

            return [
                'is_free' => $deliveryInfo['is_free'],
                'cost' => $deliveryInfo['cost'],
                'min_cost' => $deliveryMethod->price,
                'parcel_count' => $deliveryInfo['parcel_count'],
                'description' => $deliveryInfo['description'],
                'breakdown' => $deliveryInfo['breakdown'] ?? [],
                'free_shipping_threshold' => 149, // From ParcelCalculationService
                'amount_left_for_free' => max(0, 149 - $subtotal),
                'calculation_method' => $deliveryInfo['method'],
                'delivery_method_id' => $deliveryMethod->id,
                'delivery_method_name' => $deliveryMethod->name
            ];
        } catch (\Exception $e) {
            // Fallback to traditional calculation
            return $this->getTraditionalDeliveryInfo($subtotal, $deliveryMethod);
        }
    }

    /**
     * Get traditional delivery information for a specific method
     *
     * @param float $subtotal
     * @param \App\Models\DeliveryMethod $deliveryMethod
     * @return array
     */
    protected function getTraditionalDeliveryInfo(float $subtotal, $deliveryMethod): array
    {
        $isFree = $deliveryMethod->min_cart_amount > 0 && $subtotal >= $deliveryMethod->min_cart_amount;

        return [
            'is_free' => $isFree,
            'cost' => $isFree ? 0 : $deliveryMethod->price,
            'min_cost' => $deliveryMethod->price,
            'parcel_count' => 1,
            'description' => $isFree ? 'Free delivery' : 'Standard delivery: ' . number_format($deliveryMethod->price, 2) . ' PLN',
            'breakdown' => [],
            'free_shipping_threshold' => $deliveryMethod->min_cart_amount,
            'amount_left_for_free' => $isFree ? 0 : max(0, $deliveryMethod->min_cart_amount - $subtotal),
            'calculation_method' => 'traditional',
            'delivery_method_id' => $deliveryMethod->id,
            'delivery_method_name' => $deliveryMethod->name
        ];
    }

    /**
     * Get traditional delivery information for all methods
     *
     * @param \Illuminate\Support\Collection $deliveryMethods
     * @param float $subtotal
     * @return array
     */
    protected function getTraditionalDeliveryInfoForAllMethods($deliveryMethods, float $subtotal): array
    {
        $freeShippingThreshold = $deliveryMethods->min('min_cart_amount');

        // If any delivery method has free shipping threshold and cart meets it
        if ($freeShippingThreshold > 0 && $subtotal >= $freeShippingThreshold) {
            return [
                'is_free' => true,
                'min_cost' => 0,
                'costs' => [],
                'parcel_count' => 1,
                'description' => 'Free delivery',
                'breakdown' => [],
                'free_shipping_threshold' => $freeShippingThreshold,
                'amount_left_for_free' => 0,
                'calculation_method' => 'traditional'
            ];
        }

        $costs = $deliveryMethods->pluck('price')->unique()->sort()->values();

        return [
            'is_free' => false,
            'min_cost' => $costs->min(),
            'costs' => $costs,
            'parcel_count' => 1,
            'description' => 'Standard delivery from: ' . number_format($costs->min(), 2) . ' PLN',
            'breakdown' => [],
            'free_shipping_threshold' => $freeShippingThreshold,
            'amount_left_for_free' => $freeShippingThreshold > 0 ?
                max(0, $freeShippingThreshold - $subtotal) : 0,
            'calculation_method' => 'traditional'
        ];
    }
}

