<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Cart Layout Preview - Zoo Mall</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* New mobile cart styles - completely separate from desktop */
        @media (max-width: 768px) {
            /* Hide desktop table completely on mobile */
            .andro_responsive-table.cart_table {
                display: none !important;
            }

            /* Mobile cart items - independent from table */
            .mobile-cart-item {
                background: #fff;
                border-radius: 8px;
                margin-bottom: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                border: 1px solid #f0f2f3;
                padding: 15px;
                position: relative;
            }

            /* Mobile cart item header with product info and delete button */
            .mobile-cart-header {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 12px;
                position: relative;
            }

            .mobile-cart-image {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 6px;
                flex-shrink: 0;
            }

            .mobile-cart-product-info {
                flex: 1;
                min-width: 0;
            }

            .mobile-cart-product-name {
                font-size: 14px;
                line-height: 1.3;
                margin-bottom: 0;
                font-weight: 600;
            }

            .mobile-cart-product-name a {
                color: #18181d;
                text-decoration: none;
            }

            .mobile-cart-product-name a:hover {
                color: #fab725;
            }

            .mobile-cart-delete {
                position: absolute;
                top: -5px;
                right: -5px;
                width: 32px;
                height: 32px;
                min-width: 44px;
                min-height: 44px;
                background: #fff;
                border: 2px solid #dc3545;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;
                box-shadow: 0 2px 6px rgba(220, 53, 69, 0.2);
                padding: 0;
            }

            .mobile-cart-delete:hover {
                background: #dc3545;
                border-color: #dc3545;
                transform: scale(1.05);
                box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
            }

            .mobile-cart-delete:focus {
                outline: 2px solid #dc3545;
                outline-offset: 2px;
            }

            .mobile-cart-delete span {
                position: absolute;
                display: block;
                width: 14px;
                height: 2px;
                background: #dc3545;
                border-radius: 1px;
                transition: background-color 0.2s ease;
            }

            .mobile-cart-delete:hover span {
                background: #fff;
            }

            .mobile-cart-delete span:first-child {
                transform: rotate(45deg);
            }

            .mobile-cart-delete span:last-child {
                transform: rotate(-45deg);
            }

            /* Mobile cart controls - price, quantity, total */
            .mobile-cart-controls {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 12px;
                padding-top: 12px;
                border-top: 1px solid #f0f2f3;
            }

            .mobile-price {
                font-size: 14px;
                font-weight: 600;
                color: #18181d;
            }

            .mobile-price .original-price {
                font-size: 12px;
                color: #fab725;
                text-decoration: line-through;
                font-weight: 400;
                margin-right: 4px;
            }

            /* Mobile quantity controls */
            .mobile-qty-control {
                display: flex;
                align-items: center;
                gap: 8px;
                background: #f8f9fa;
                border-radius: 20px;
                padding: 4px 8px;
            }

            .mobile-qty-control .qty-subtract,
            .mobile-qty-control .qty-add {
                width: 32px;
                height: 32px;
                min-width: 44px;
                min-height: 44px;
                border-radius: 50%;
                background: #fff;
                border: 1px solid #dee2e6;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;
                padding: 0;
            }

            .mobile-qty-control .qty-subtract:hover,
            .mobile-qty-control .qty-add:hover {
                background: #fab725;
                border-color: #fab725;
                color: #fff;
            }

            .mobile-qty-control input {
                width: 40px;
                text-align: center;
                border: none;
                background: transparent;
                font-weight: 600;
                font-size: 14px;
            }

            .mobile-total {
                font-size: 16px;
                font-weight: 700;
                color: #18181d;
            }
        }
        
        /* General styles */
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            max-width: 500px;
            margin: 20px auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        /* Desktop styles (when not mobile) */
        @media (min-width: 769px) {
            .mobile-cart-item {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mobile Cart Layout Preview - New Structure</h1>

        <!-- Desktop table (hidden on mobile) -->
        <table class="andro_responsive-table cart_table d-none d-md-table">
            <thead>
                <tr>
                    <th>Remove</th>
                    <th>Product</th>
                    <th>Price</th>
                    <th>Quantity</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="5">Desktop table content (hidden on mobile)</td>
                </tr>
            </tbody>
        </table>

        <!-- Mobile cart items (rendered directly, hidden on desktop) -->
        <!-- Sample Cart Item 1 -->
        <div class="mobile-cart-item d-md-none" id="mobile-item-1">
            <!-- Mobile cart header with product info and delete button -->
            <div class="mobile-cart-header">
                <img src="https://via.placeholder.com/60x60/fab725/ffffff?text=Pet+Food" alt="Premium Dog Food" class="mobile-cart-image">

                <div class="mobile-cart-product-info">
                    <h6 class="mobile-cart-product-name">
                        <a href="#" style="color: #18181d; text-decoration: none;">
                            Premium Dog Food - Chicken & Rice 15kg
                        </a>
                    </h6>
                </div>

                <button type="button" class="mobile-cart-delete">
                    <span></span>
                    <span></span>
                </button>
            </div>

            <!-- Mobile cart controls - price, quantity, total -->
            <div class="mobile-cart-controls">
                <div class="mobile-price">
                    <span class="original-price">89.99 zł</span>
                    <span>79.99 zł</span>
                </div>

                <div class="mobile-qty-control qty">
                    <span class="qty-subtract">
                        <i class="fa fa-minus"></i>
                    </span>
                    <input type="number" name="qty" value="2">
                    <span class="qty-add">
                        <i class="fa fa-plus"></i>
                    </span>
                </div>

                <div class="mobile-total">
                    159.98 zł
                </div>
            </div>
        </div>

        <!-- Sample Cart Item 2 -->
        <div class="mobile-cart-item d-md-none" id="mobile-item-2">
            <!-- Mobile cart header with product info and delete button -->
            <div class="mobile-cart-header">
                <img src="https://via.placeholder.com/60x60/28a745/ffffff?text=Cat+Toy" alt="Interactive Cat Toy" class="mobile-cart-image">

                <div class="mobile-cart-product-info">
                    <h6 class="mobile-cart-product-name">
                        <a href="#" style="color: #18181d; text-decoration: none;">
                            Interactive Cat Toy with Feathers
                        </a>
                    </h6>
                </div>

                <button type="button" class="mobile-cart-delete">
                    <span></span>
                    <span></span>
                </button>
            </div>

            <!-- Mobile cart controls - price, quantity, total -->
            <div class="mobile-cart-controls">
                <div class="mobile-price">
                    <span>24.99 zł</span>
                </div>

                <div class="mobile-qty-control qty">
                    <span class="qty-subtract">
                        <i class="fa fa-minus"></i>
                    </span>
                    <input type="number" name="qty" value="1">
                    <span class="qty-add">
                        <i class="fa fa-plus"></i>
                    </span>
                </div>

                <div class="mobile-total">
                    24.99 zł
                </div>
            </div>
        </div>
        
        <div class="alert alert-info">
            <strong>Mobile Optimizations:</strong>
            <ul class="mb-0 mt-2">
                <li>Compact horizontal card layout</li>
                <li>Product images now visible on mobile</li>
                <li>Touch-friendly quantity controls</li>
                <li>Reduced vertical spacing</li>
                <li>Better information hierarchy</li>
            </ul>
        </div>
    </div>
</body>
</html>
