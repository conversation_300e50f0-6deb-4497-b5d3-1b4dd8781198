<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Cart Layout Preview - Zoo Mall</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Import the mobile cart styles */
        @media (max-width: 768px) {
            /* Compact mobile cart layout */
            .andro_responsive-table.cart_table {
                border: none;
                margin-bottom: 15px;
            }
            
            .andro_responsive-table.cart_table tr {
                background: #fff;
                border-radius: 8px;
                margin-bottom: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                border: 1px solid #f0f2f3;
                padding: 12px;
                display: block;
            }
            
            .andro_responsive-table.cart_table td {
                border: none;
                padding: 4px 0;
                display: block;
                text-align: left;
                background: transparent;
            }
            
            .andro_responsive-table.cart_table td::before {
                display: none;
            }
            
            /* Mobile cart item layout - horizontal card style */
            .andro_responsive-table.cart_table tr {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            /* Product section - horizontal layout */
            .andro_responsive-table.cart_table td[data-title="Product"] {
                order: 1;
                flex: 1;
            }
            
            .andro_responsive-table.cart_table .andro_cart-product-wrapper {
                display: flex;
                align-items: center;
                gap: 12px;
                flex-direction: row;
                padding: 0;
            }
            
            .andro_responsive-table.cart_table .andro_cart-product-wrapper img,
            .andro_responsive-table.cart_table .andro_cart-product-wrapper .cart-product-img {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 6px;
                margin: 0;
                flex-shrink: 0;
                display: block !important;
            }
            
            .andro_responsive-table.cart_table .andro_cart-product-body {
                flex: 1;
                min-width: 0;
            }
            
            .andro_responsive-table.cart_table .andro_cart-product-body h6 {
                font-size: 14px;
                line-height: 1.3;
                margin-bottom: 2px;
                font-weight: 600;
            }
            
            /* Price, quantity, and total in a horizontal row */
            .mobile-cart-controls {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 12px;
                order: 2;
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid #f0f2f3;
            }
            
            .andro_responsive-table.cart_table td.price,
            .andro_responsive-table.cart_table td.quantity,
            .andro_responsive-table.cart_table td.sum {
                display: none;
            }
            
            .andro_responsive-table.cart_table td.remove {
                position: absolute;
                top: 8px;
                right: 8px;
                order: 0;
                padding: 0;
            }
            
            .andro_responsive-table.cart_table .close-btn {
                width: 24px;
                height: 24px;
                opacity: 0.6;
                background: none;
                border: none;
                cursor: pointer;
            }
            
            /* Mobile quantity controls */
            .mobile-qty-control {
                display: flex;
                align-items: center;
                gap: 8px;
                background: #f8f9fa;
                border-radius: 20px;
                padding: 4px 8px;
            }
            
            .mobile-qty-control .qty-subtract,
            .mobile-qty-control .qty-add {
                width: 28px;
                height: 28px;
                border-radius: 50%;
                background: #fff;
                border: 1px solid #dee2e6;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .mobile-qty-control .qty-subtract:hover,
            .mobile-qty-control .qty-add:hover {
                background: #fab725;
                border-color: #fab725;
                color: #fff;
            }
            
            .mobile-qty-control input {
                width: 40px;
                text-align: center;
                border: none;
                background: transparent;
                font-weight: 600;
                font-size: 14px;
            }
            
            /* Mobile price display */
            .mobile-price {
                font-size: 14px;
                font-weight: 600;
                color: #18181d;
            }
            
            .mobile-price .original-price {
                font-size: 12px;
                color: #fab725;
                text-decoration: line-through;
                font-weight: 400;
                margin-right: 4px;
            }
            
            .mobile-total {
                font-size: 16px;
                font-weight: 700;
                color: #18181d;
            }
        }
        
        /* General styles */
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            max-width: 500px;
            margin: 20px auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .close-btn span {
            display: block;
            width: 12px;
            height: 2px;
            background: #dc3545;
            margin: 2px 0;
            transform-origin: center;
        }
        
        .close-btn span:first-child {
            transform: rotate(45deg) translate(2px, 2px);
        }
        
        .close-btn span:last-child {
            transform: rotate(-45deg) translate(2px, -2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mobile Cart Layout Preview</h1>
        
        <table class="andro_responsive-table cart_table">
            <tbody>
                <!-- Sample Cart Item 1 -->
                <tr id="item-1">
                    <td class="remove">
                        <button type="button" class="close-btn close-danger">
                            <span></span>
                            <span></span>
                        </button>
                    </td>
                    <td data-title="Product">
                        <div class="andro_cart-product-wrapper">
                            <img src="https://via.placeholder.com/60x60/fab725/ffffff?text=Pet+Food" alt="Premium Dog Food" class="cart-product-img">
                            <div class="andro_cart-product-body">
                                <h6>
                                    <a href="#" style="color: #18181d; text-decoration: none;">
                                        Premium Dog Food - Chicken & Rice 15kg
                                    </a>
                                </h6>
                            </div>
                        </div>
                    </td>
                    
                    <!-- Mobile-optimized controls -->
                    <div class="mobile-cart-controls d-md-none">
                        <div class="mobile-price">
                            <span class="original-price">89.99 zł</span>
                            <span>79.99 zł</span>
                        </div>
                        
                        <div class="mobile-qty-control qty">
                            <span class="qty-subtract">
                                <i class="fa fa-minus"></i>
                            </span>
                            <input type="number" name="qty" value="2">
                            <span class="qty-add">
                                <i class="fa fa-plus"></i>
                            </span>
                        </div>
                        
                        <div class="mobile-total">
                            159.98 zł
                        </div>
                    </div>
                </tr>
                
                <!-- Sample Cart Item 2 -->
                <tr id="item-2">
                    <td class="remove">
                        <button type="button" class="close-btn close-danger">
                            <span></span>
                            <span></span>
                        </button>
                    </td>
                    <td data-title="Product">
                        <div class="andro_cart-product-wrapper">
                            <img src="https://via.placeholder.com/60x60/28a745/ffffff?text=Cat+Toy" alt="Interactive Cat Toy" class="cart-product-img">
                            <div class="andro_cart-product-body">
                                <h6>
                                    <a href="#" style="color: #18181d; text-decoration: none;">
                                        Interactive Cat Toy with Feathers
                                    </a>
                                </h6>
                            </div>
                        </div>
                    </td>
                    
                    <!-- Mobile-optimized controls -->
                    <div class="mobile-cart-controls d-md-none">
                        <div class="mobile-price">
                            <span>24.99 zł</span>
                        </div>
                        
                        <div class="mobile-qty-control qty">
                            <span class="qty-subtract">
                                <i class="fa fa-minus"></i>
                            </span>
                            <input type="number" name="qty" value="1">
                            <span class="qty-add">
                                <i class="fa fa-plus"></i>
                            </span>
                        </div>
                        
                        <div class="mobile-total">
                            24.99 zł
                        </div>
                    </div>
                </tr>
            </tbody>
        </table>
        
        <div class="alert alert-info">
            <strong>Mobile Optimizations:</strong>
            <ul class="mb-0 mt-2">
                <li>Compact horizontal card layout</li>
                <li>Product images now visible on mobile</li>
                <li>Touch-friendly quantity controls</li>
                <li>Reduced vertical spacing</li>
                <li>Better information hierarchy</li>
            </ul>
        </div>
    </div>
</body>
</html>
