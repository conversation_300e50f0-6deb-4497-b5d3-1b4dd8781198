
<!-- Mobile cart items will be rendered directly by the item template -->
@foreach ($cartItems as $id => $item)
    @include('app.catalog.cart.item_mobile', ['item' => $item, 'id' => $id])
@endforeach

<!-- Desktop Cart Table (hidden on mobile) -->
<table class="andro_responsive-table cart_table d-none d-md-table">
    <thead>
        <tr>
            <th class="remove-item"></th>
            <th>Produkt</th>
            <th>Cena</th>
            <th><PERSON><PERSON><PERSON><PERSON></th>
            <th><PERSON><PERSON><PERSON><PERSON></th>
        </tr>
    </thead>
    <tbody>
        @foreach ($cartItems as $id => $item)
            @include('app.catalog.cart.item', ['item' => $item, 'id' => $id])
        @endforeach
    </tbody>
</table>

<input type="hidden" name="total" value="{{ $total }}">
<!-- Cart End -->
