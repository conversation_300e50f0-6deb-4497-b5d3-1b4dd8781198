<!-- Desktop table row (hidden on mobile) -->
<tr id="{{$item->id}}" class="d-none d-md-table-row">
    <td class="remove">
        <button type="button" class="close-btn close-danger remove-from-cart" data-delete-url="{{ route('cart.delete', ['product' => $item->product]) }}">
            <span></span>
            <span></span>
        </button>
    </td>
    <td data-title="Product">
        @include('app.catalog.cart.item.detail', ['product' => $item->product])
    </td>
    <td data-title="Cena" class="price">
        @include('app.catalog.cart.item.price', ['product' => $item->product])
    </td>
    <td class="quantity remove" data-title="Ilość">
        <div class="qty" data-id="{{ $item->id }}" data-update-url="{{ route('cart.update', ['product' => $item->product]) }}">
            <span class="qty-subtract"><i class="fa fa-minus"></i></span>
            <input type="number" name="qty" value="{{ $item->quantity }}" data-id="{{ $item->id }}">
            <span class="qty-add"><i class="fa fa-plus"></i></span>
        </div>
    </td>
    <td data-title="Wartość" class="sum">
        @if ($item->discount)
            <span class="@if($item->subtotal == $item->product->price * $item->quantity)d-none @endif">
                {{ number_format($item->product->price * $item->quantity, 2) }} zł
            </span>
            <br/>
            <strong>{{ number_format($item->subtotal, 2) }} zł</strong>
        @else
            <strong>{{ number_format($item->subtotal, 2) }} zł</strong>
        @endif
    </td>
</tr>
